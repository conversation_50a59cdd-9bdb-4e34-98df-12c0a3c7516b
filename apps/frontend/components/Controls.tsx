/**
 * 矩阵控制组件
 * 🎯 核心价值：纯UI控制组件，配置驱动，零业务逻辑
 * 📦 功能范围：模式切换、配置调整、状态显示
 * 🔄 架构设计：完全无状态组件，所有逻辑通过状态管理注入
 */

'use client';

import Button from '@/components/ui/Button';
import CascadeSelect from '@/components/ui/CascadeSelect';
import { RefreshIcon } from '@/components/ui/Icons';
import WordLibraryManager from '@/components/WordLibraryManager';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { BusinessMode, ContentMode, MainMode } from '@/core/matrix/MatrixTypes';
import React, { memo, useCallback } from 'react';

// ===== 组件属性 =====

interface ControlsProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 显示配置 */
  showModeSelector?: boolean;
  showStatusBar?: boolean;

  /** 事件回调 */
  onModeChange?: (mode: BusinessMode) => void;
  onModeConfigChange?: (mainMode: MainMode, contentMode: ContentMode) => void;
  onReset?: () => void;
}

// ===== 统一的模式配置 =====

const MODE_LABELS: Record<BusinessMode, string> = {
  coordinate: '坐标',
  color: '颜色',
  level: '等级',
  word: '词语',
};

// ===== 主控制组件 =====

const ControlsComponent: React.FC<ControlsProps> = ({
  className = '',
  style,
  showModeSelector = true,
  showStatusBar = true,
  onModeChange,
  onModeConfigChange,
  onReset,
}) => {
  const {
    data,
    config,
    setModeConfig,
    getDataAvailability,
    initializeMatrix
  } = useMatrixStore();

  const mode = config.mode;
  const mainMode = config.mainMode || 'default';
  const contentMode = config.contentMode || 'blank';
  const selectedCells = data.selectedCells;

  // 获取数据可用性
  const dataAvailability = getDataAvailability();



  // 处理新模式配置切换
  const handleModeConfigChange = useCallback((newMainMode: MainMode, newContentMode: ContentMode) => {
    setModeConfig(newMainMode, newContentMode);
    onModeConfigChange?.(newMainMode, newContentMode);
  }, [setModeConfig, onModeConfigChange]);



  // 处理重置
  const handleReset = useCallback(() => {
    // 重置到默认模式下的空白模式
    setModeConfig('default', 'blank');
    initializeMatrix();
    onReset?.();
  }, [setModeConfig, initializeMatrix, onReset]);

  return (
    <div className={`controls-container ${className} h-full flex flex-col`} style={style}>
      {/* 可滚动的内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-6">
          {/* 新模式选择器 */}
          {showModeSelector && (
            <div className="mode-selector">
              {/* <label className="block text-sm font-medium text-gray-700 mb-2">
                显示模式
              </label> */}
              <CascadeSelect
                mainMode={mainMode}
                contentMode={contentMode}
                onModeChange={handleModeConfigChange}
                dataAvailability={dataAvailability}
              />
            </div>
          )}



          {/* 操作按钮 */}
          <div className="space-y-2">
            <Button
              variant="danger"
              onClick={handleReset}
              className="w-full"
            >
              <RefreshIcon size={16} className="mr-2" />
              重置矩阵
            </Button>
          </div>

          {/* 词库管理模块 */}
          <div className="mt-6">
            <WordLibraryManager />
          </div>
        </div>
      </div>

      {/* 状态栏 - 固定在底部 */}
      {showStatusBar && (
        <div className="status-bar p-3 bg-gray-50 border-t text-sm text-gray-600 flex-shrink-0">
          <div className="flex items-center space-x-4">
            <span>模式: {MODE_LABELS[mode]}</span>
            <span>已选择: {selectedCells.size}</span>
            <span>总计: 1089</span>
          </div>
        </div>
      )}
    </div>
  );
};

// ===== 性能优化 =====

const Controls = memo(ControlsComponent);

Controls.displayName = 'Controls';

export default Controls;
